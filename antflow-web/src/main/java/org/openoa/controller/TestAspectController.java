package org.openoa.controller;

import lombok.extern.slf4j.Slf4j;
import org.openoa.base.entity.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试切面的Controller
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class TestAspectController {

    @GetMapping("/aspect")
    public Result testAspect() {
        log.info("TestAspectController.testAspect() 方法被调用");
        return Result.newSuccessResult("切面测试成功");
    }

    @GetMapping("/simple")
    public String simpleTest() {
        log.info("TestAspectController.simpleTest() 方法被调用");
        return "简单测试";
    }
}
