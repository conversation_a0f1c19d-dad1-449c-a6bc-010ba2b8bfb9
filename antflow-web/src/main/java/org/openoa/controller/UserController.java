package org.openoa.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.impl.cfg.multitenant.TenantInfoHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.openoa.base.dto.PageDto;
import org.openoa.base.dto.wecom.WechatConfigProperties;
import org.openoa.base.entity.Result;
import org.openoa.base.entity.UserMessage;
import org.openoa.base.interf.anno.IgnoreLog;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.AfUserService;
import org.openoa.base.util.PageUtils;
import org.openoa.base.vo.*;
import org.openoa.common.enums.QueryUserOrgTagTypeEnum;
import org.openoa.engine.bpmnconf.service.impl.UserMessageServiceImpl;
import org.openoa.locate.factory.QueryUserDynamicCalculateFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@IgnoreLog
@RequestMapping("/user")
@RestController
@Slf4j
public class UserController {
    @Autowired
    private AfUserService userService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private TenantInfoHolder infoHolder;
	@Resource
	private UserMessageServiceImpl userMessageService;
	@Resource
	private WechatConfigProperties wechatConfigProperties;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
	@Resource
	private QueryUserDynamicCalculateFactory queryUserDynamicCalculateFactory;

    @RequestMapping("/queryUserByNameFuzzy")
    public Result queryUserByNameFuzzy(String name){
        if(StringUtils.isEmpty(name)){
            return Result.newSuccessResult(Lists.newArrayList());
        }
        List<OrgEmpTagInfoVo> users =  userService.queryUserByNameFuzzy(name);
        return Result.newSuccessResult(users);
    }

    @RequestMapping("/queryUserTagByNameFuzzy")
    public Result queryUserTagByNameFuzzy(String name){
        if(StringUtils.isEmpty(name)){
            return Result.newSuccessResult(Lists.newArrayList());
        }
        List<OrgEmpTagInfoVo> users =  userService.queryUserByNameFuzzy(name);
        List<OrgEmpTagInfoVo> tags =  userService.queryTagByNameFuzzy(name);
        return Result.newSuccessResult(
                new HashMap<String, List<OrgEmpTagInfoVo>>() {
                    {
                        put("users", users);
                        put("tags", tags);
                    }
                }
        );
    }

    @RequestMapping("/queryUserOrgTagByNameFuzzy")
    public Result queryUserOrgTagByNameFuzzy(String name){
        if(StringUtils.isEmpty(name)){
            return Result.newSuccessResult(Lists.newArrayList());
        }
        List<OrgEmpTagInfoVo> users =  userService.queryUserByNameFuzzy(name);
        List<OrgEmpTagInfoVo> tags =  userService.queryTagByNameFuzzy(name);
        List<OrgEmpTagInfoVo> orgs =  userService.queryOrgByNameFuzzy(name);
        return Result.newSuccessResult(
                new HashMap<String, List<OrgEmpTagInfoVo>>() {
                    {
                        put("users", users);
                        put("tags", tags);
                        put("orgs", orgs);
                    }
                }
        );
    }

    @RequestMapping("/queryCompanyByNameFuzzy")
    public Result queryCompanyByNameFuzzy(String companyName){
        List<BaseIdTranStruVo> codeTranStruVos = userService.queryCompanyByNameFuzzy(companyName);
        return Result.newSuccessResult(codeTranStruVos);
    }

    /**
     * 获取全部人员信息
     * @param roleId
     * @return
     */
    @GetMapping(value ={"/getUser/{roleId}","/getUser"})
    public Result<List<BaseIdTranStruVo>> getUsers(@PathVariable(required = false) Integer roleId) {
        LinkedList<BaseIdTranStruVo> list = userMapper.selectAll(roleId);
        return Result.newSuccessResult(list);

    }

    /**
     * 获取人员分页信息
     * @param requestDto
     * @return
     */
    @PostMapping("/getUserPageList")
    public ResultAndPage<BaseIdTranStruVo> getUserPageList( @RequestBody DetailRequestDto requestDto){
        PageDto pageDto = requestDto.getPageDto();
        Page<BaseIdTranStruVo> page = PageUtils.getPageByPageDto(pageDto);
        TaskMgmtVO taskMgmtVO = requestDto.getTaskMgmtVO();
        List<BaseIdTranStruVo> results = userMapper.selectUserPageList(page, taskMgmtVO);
        page.setRecords(results);
        return PageUtils.getResultAndPage(page);
    }


    /**
     * 根据Source获取对应组织的部门、人员Tree信息
     */
    @PostMapping("/getOrgTree")
    public Result<OrgEmpTreeVo> getOrgTree(@Valid @RequestBody EmpOrOrgTreeReqDto req){
        OrgEmpTreeVo orgEmpTreeVo = userService.getOrgEmpTreeBySource(req.getEhrSource(), req.getType());
        return Result.newSuccessResult(orgEmpTreeVo);
    }

	/**
	 * 用户未处理消息列表
	 */
	@PostMapping("/messages")
	public ResultAndPage<UserMessage> getUserMessages(@RequestBody PageDto pageDto){
		return userMessageService.page(pageDto);
	}

	/**
	 * 用户未处理消息个数
	 */
	@GetMapping("/messages/unReadCount")
	public Result<Long> messagesUnReadCount(){
		return userMessageService.messagesUnReadCount();
	}

	/**
	 * 更新消息已读状态
	 */
	@PostMapping("/messages/{id}")
	public Result<Boolean> getUserMessages(@PathVariable("id") Long id){
		return userMessageService.readMessage(id);
	}


	/**
	 * 通用的用户、部门、标签的组合模糊查询
	 * @param name 名称
	 * @param queryType 查询类型 1-根据用户名模糊搜索 2-根据组织名称模糊搜索 3-根据用户组织模糊搜索 4-根据用户标签模糊搜索 5-根据用户组织标签模糊搜索
	 * @return List
	 */
	@RequestMapping("/queryByNameFuzzy")
	public Result<Map<String, List<OrgEmpTagInfoVo>>> queryByNameFuzzy(String name, Integer queryType) {
		if (StringUtils.isBlank(name)) {
			return Result.newSuccessResult(new HashMap<>());
		}
		return Result.newSuccessResult(queryUserDynamicCalculateFactory.getDynamicCalc(QueryUserOrgTagTypeEnum.of(queryType)).locateQuery(name));
	}
}
