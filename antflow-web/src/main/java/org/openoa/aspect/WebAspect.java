package org.openoa.aspect;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * dubbo业务切面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 20:48
 **/
@Aspect
@Component
@Slf4j
public class WebAspect {

    /**
     * 定义cat监控扫描路径
     * 定义切点
     *
     * <AUTHOR>
     * @date 2023/4/6 20:48
     **/
    @Pointcut("execution(public * org.openoa.controller..*.*(..))")
    public void requestConfigPointCut() {
    }

    /**
     * 对于监控到的方法进行监控增强处理
     * 定义环绕类型的Advise（增强器）
     *
     * @param joinPoint 切面连接点（被代理方法的相关封装）
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2023/4/6 20:48
     */
    @Around("requestConfigPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        //添加cat监控
        Class<?> clazz = joinPoint.getTarget().getClass();
        String className = clazz.getSimpleName();
        Signature signature = joinPoint.getSignature();
        String methodName = signature.getName();
        Transaction serviceCat = Cat.newTransaction("Controller", className + "." + methodName);
        serviceCat.setStatus(Transaction.SUCCESS);
        Object proceed = null;
        try {
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            serviceCat.setStatus(e);
            Cat.logError(e);
            log.warn(className + "," + methodName + "方法异常:", e);
            throw e;
        } finally {
            serviceCat.complete();
        }
        return proceed;
    }
}
