package org.openoa;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@SpringBootApplication
@Slf4j
public class AntFlowApplication {

    public static void main(String[] args) throws Exception {
        SpringApplication.run(AntFlowApplication.class, args);
		log.error("AntFlow 项目启动成功!");
    }


}
