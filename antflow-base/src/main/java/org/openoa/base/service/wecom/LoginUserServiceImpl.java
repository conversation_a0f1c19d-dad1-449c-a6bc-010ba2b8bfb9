package org.openoa.base.service.wecom;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.openoa.base.dto.wecom.EmployeeInfo;
import org.openoa.base.entity.Employee;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.vo.LoginUser;
import org.openoa.base.vo.WebResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description
 * <AUTHOR>
 * @date 2025-02-17
 **/
@Service
@Slf4j
public class LoginUserServiceImpl implements LoginUserService {

    @Resource
    private GatewayApi gatewayApi;

    @Value("${gateway.flowApi.url}")
    private String flowApiUrl;
	@Resource
	private EhrSyncService ehrSyncService;
    @Resource
    private TokenService tokenService;
	@Resource
	private UserMapper userMapper;

    /***
     * 根据 source + userId 获取用户信息
     * @param source
     * @param userId 企微用户id
     * <AUTHOR>
     * @date 2025/2/17 14:24
     * @version 1.0.0
     * @return java.util.Optional<com.ce.contract.boss.login.vo.LoginUser>
     **/
    @Override
    public Optional<LoginUser> getLoginUser(Integer source, String userId) {
        // 获取用户缓存
        Optional<String> loginUserData = tokenService.getLoginUserData(source, userId);
        if (loginUserData.isPresent()) {
            LoginUser loginUser = JSON.parseObject(loginUserData.get(), LoginUser.class);
            log.info("获取缓存的登录用户信息 {}",JSON.toJSONString(loginUser));
            return Optional.of(loginUser);
        }

        // 根据 source + userId 获取用户信息
        Map<String, Object> params = new HashMap<>();
        String uriApi = flowApiUrl + "/login/getLoginInfo";
        Transaction serviceCat = Cat.newTransaction("SERVICE", "getLoginUser");
        serviceCat.setStatus(Transaction.SUCCESS);
        try {
            params.put("employeeId", userId);
            params.put("source", source);
            String response = gatewayApi.request(uriApi, params);
            log.info("getLoginUser response={}", response);
            WebResult webResult = JSON.toJavaObject(JSONObject.parseObject(response), WebResult.class);
            String code = webResult.getCode();
            if (!Objects.equals(code, "0")) {
                log.error("getLoginUser error={}", JSON.toJSONString(webResult));
                return Optional.empty();
            }

            Object data = webResult.getData();
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(data));
            LoginUser loginUser = generateLoginUser(jsonObject);
            // 添加用户缓存
            tokenService.saveLoginUserData(source,userId,JSON.toJSONString(loginUser));
            log.info("getLoginUser loginUser={}", loginUser);
            return Optional.of(loginUser);
        }catch (Exception e) {
            serviceCat.setStatus(e);
            Cat.logError(e);
            log.error("getLoginUser发生异常 uri={} 参数={}",uriApi, JSON.toJSONString(params), e);
        }finally {
            serviceCat.complete();
        }
        return Optional.empty();
    }

	@Override
	public Optional<LoginUser> getLoginUserByUserId(Integer source, String employeeId) {
		Employee employeeDetailById = userMapper.getEmployeeDetailById(employeeId);
		if (employeeDetailById == null) {
			return Optional.empty();
		}
		LoginUser loginUser = new LoginUser();
		loginUser.setUserId(employeeDetailById.getId());
		loginUser.setUserName(employeeDetailById.getUsername());
		loginUser.setEmail(employeeDetailById.getEmail());
		loginUser.setMobile(employeeDetailById.getMobile());
		return Optional.of(loginUser);
	}

	/***
     * 根据企微的userId获取对应的ehr信息
     * @param source
     * @param userId
     * <AUTHOR>
     * @date 2025/4/18 17:56
     * @version 1.0.0
     * @return java.util.Optional<com.ce.contract.boss.login.vo.LoginUser>
    **/
    @Override
    public Optional<LoginUser> getByWechatUserId(Integer source, String userId) {
        Map<String, Object> params = new HashMap<>();
        Transaction serviceCat = Cat.newTransaction("SERVICE", "getByWechatUserId");
        serviceCat.setStatus(Transaction.SUCCESS);
        try {
            params.put("userId", userId);
            params.put("source", source);
	        EmployeeInfo employeeInfo = ehrSyncService.getByWechatUserId(source, userId);
            log.info("getByWechatUserId response={}", JSON.toJSONString(employeeInfo));
			if (Objects.isNull(employeeInfo)){
				return Optional.empty();
			}

            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(employeeInfo));
            LoginUser loginUser = generateLoginUser(jsonObject);
            log.info("getByWechatUserId ret={}", JSON.toJSONString(loginUser));
            return Optional.of(loginUser);
        }catch (Exception e) {
            serviceCat.setStatus(e);
            Cat.logError(e);
            log.warn("getByWechatUserId发生异常 参数={}", JSON.toJSONString(params), e);
        }finally {
            serviceCat.complete();
        }
        return Optional.empty();
    }

    public static void main(String[] args) {
        String response = "{\"code\":\"0\",\"msg\":\"OK\",\"data\":{\"id\":12,\"source\":4,\"employeeId\":\"87116\",\"name\":\"高美晶\",\"orgId\":\"3888\",\"state\":1,\"createTime\":\"2025-02-18 09:16:46\",\"updateTime\":\"2025-02-18 09:16:46\"}}" ;
        WebResult webResult = JSON.toJavaObject(JSONObject.parseObject(response), WebResult.class);
        if (!webResult.checkSuccess()) {
            log.error("getLoginUser error={}", webResult);
        }
        Object data = webResult.getData();
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(data));
        String userName = jsonObject.getString("name");
        String employeeId = jsonObject.getString("employeeId");
        LoginUser loginUser = new LoginUser();
        loginUser.setUserName(userName);
        loginUser.setUserId(employeeId);
        String orgId = jsonObject.getString("orgId");
        loginUser.setDeptId(orgId);
        System.out.println(JSON.toJSONString(loginUser));
    }
    public static LoginUser generateLoginUser(JSONObject jsonObject) {
        /*
         *     {
         *         "realEmployeeId": "95478",
         *         "employeeId": "95478",
         *         "businessId": "95478",
         *         "employeeName": "吕凯",
         *         "organizationId": "3605",
         *         "organizationName": "CDP",
         *         "email": "<EMAIL>",
         *         "ehrSource": 1
         *     }
         */
        String userName = jsonObject.getString("employeeName");
        String employeeId = jsonObject.getString("employeeId");
        String businessId = jsonObject.getString("businessId");
        LoginUser loginUser = new LoginUser();
        loginUser.setUserName(userName);
        loginUser.setUserId(employeeId);
        String orgId = jsonObject.getString("organizationId");
        loginUser.setDeptId(orgId);
        loginUser.setSource(jsonObject.getInteger("ehrSource"));
        loginUser.setBusinessId(businessId);
        return loginUser;
    }
}