package org.openoa.base.service.wecom;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.openoa.base.dto.wecom.EHRSourceEnum;
import org.openoa.base.dto.wecom.EmployeeInfo;
import org.openoa.base.dto.wecom.RespCodeEnum;
import org.openoa.base.dto.wecom.WechatConfigProperties;
import org.openoa.base.entity.Employee;
import org.openoa.base.entity.SyncEhrOrgInfo;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.wecom.msgs.WxMsg;
import org.openoa.base.service.wecom.msgs.WxMsgMarkdown;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 企微service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 09:25
 */
@Service
@Slf4j
public class WechatService {

    private static final String sendMsgUrl = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s";
    private static final String accessTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";
    private static final String getUserIdByEmail = "https://qyapi.weixin.qq.com/cgi-bin/user/get_userid_by_email?access_token=%s";

    @Resource
    private WechatConfigProperties wechatConfigProperties;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    EhrSyncService ehrSyncService;

    @Resource
    private Environment environment;

    @Value("${wx_redirect_url}")
    private String wxRedirectUrl;

    private String approveUrl = "https://{approveDomain}/antflow/flowTask/pendding/approve";
	@Autowired
	private UserMapper userMapper;

	public Optional<String> toRealWxId(List<EmployeeInfo> empInfoList, String wxAccessToken, EHRSourceEnum sourceEnum) {
        // 这是获取的是- realEmployeeId 对应的是ehr 中原始的id 也是企微中的id
        if (EHRSourceEnum.CHEN_XING == sourceEnum) {
            //辰星的企微ID不是员工ID，因此需要根据邮箱换取企微ID来发送消息
            return empInfoList.stream().map(EmployeeInfo::getEmail)
                    .map(mail -> {
						if (Objects.equals(mail, "<EMAIL>")) { // 特殊人员判断
							mail = "<EMAIL>";
						}
                        JSONObject getWechatIdParam = new JSONObject();
                        getWechatIdParam.put("email", mail);
                        getWechatIdParam.put("email_type", 1);
                        RequestEntity<JSONObject> getUserIdRequest = RequestEntity.post(
                                URI.create(String.format(getUserIdByEmail, wxAccessToken))).contentType(MediaType.APPLICATION_JSON).body(getWechatIdParam);
                        ResponseEntity<String> getUserIdResponse = restTemplate.exchange(getUserIdRequest, String.class);
                        JSONObject getUserIdJson;
                        if (!getUserIdResponse.getStatusCode().is2xxSuccessful() || StringUtils.isEmpty(getUserIdResponse.getBody()) || (getUserIdJson = JSONObject.parseObject(getUserIdResponse.getBody())) == null || getUserIdJson.getInteger("errcode") != 0) {
                            log.error("辰星员工根据邮箱获取企微ID失败,请求email:{},返回信息为:{}", mail, JSON.toJSONString(getUserIdResponse));
                            return null;
                        }
                        return getUserIdJson.getString("userid");
                    })
                    .filter(Objects::nonNull)
                    .reduce((a, b) -> a + "|" + b);
        } else {
            return empInfoList.stream().map(EmployeeInfo::getRealEmployeeId).reduce((a, b) -> a + "|" + b);
        }
    }

    @Async
    public void sendWxMsgByBusinessId(NoticeMsgEnum noticeMsgEnum, String processNumber, String formCode, String processName
            , String businessId
            , String taskId
            , String createUserName
            , SyncEhrOrgInfo createUserOrgInfo
            , List<String> noticedUserIds
            , String submitUserId
            , String submitUserName) {
        //  businessIds = Arrays.asList("105704");
        // noticedUserIds = Arrays.asList("105704", "jt83326","84813","jt84813");

        if (CollectionUtils.isEmpty(noticedUserIds)) {
            log.error("businessIds为空");
            return;
        }
        // List<EmployeeInfo> empInfoList = this.getEmpInfoByBusinessIds(String.join(",", noticedUserIds));
	    List<Employee> empInfoList = userMapper.getEmployeeDetailByIds(noticedUserIds);
        if (CollectionUtils.isEmpty(empInfoList)) {
            log.error("businessIds对应的员工信息为空，businessIds={}", noticedUserIds);
            return;
        }

        Map<Integer, List<Employee>> empGroupBySource = empInfoList.stream().collect(Collectors.groupingBy(Employee::getEhrSource));
        empGroupBySource.forEach((source, empList) -> {
            EHRSourceEnum ehrSourceEnum = EHRSourceEnum.fromType(source);
            WechatConfigProperties.WechatConfig wechatConfig = this.getWechatConfig(ehrSourceEnum);
            String wxAccessToken = this.getAccessToken(wechatConfig.getCorpId(), wechatConfig.getCorpsecret());

            Optional<String> wxUserIds = empList.stream().map(Employee::getEmpId).reduce((a, b) -> a + "|" + b);
            if (!wxUserIds.isPresent()) {
                log.error("获取企微ID失败，empList={}", empList);
                return;
            }

            try {
                WxMsg wxMsg = buildMsg(noticeMsgEnum, processNumber, formCode, processName, businessId, taskId
                        , createUserName, createUserOrgInfo, wechatConfig.getCorpId(), wechatConfig.getApproveDomain(), submitUserId, submitUserName);

                wxMsg.setTouser(wxUserIds.get());
                wxMsg.setAgentid(wechatConfig.getAgentId());

                JSONObject params = JSONObject.from(wxMsg);

                log.error("发送企业微信消息内容:{}", JSONObject.toJSONString(params));
                RequestEntity<JSONObject> request = RequestEntity.post(URI.create(String.format(sendMsgUrl, wxAccessToken))).contentType(MediaType.APPLICATION_JSON).body(params);
                ResponseEntity<String> responseEntity = restTemplate.exchange(request, String.class);
                JSONObject responseJson;
                if (!responseEntity.getStatusCode().is2xxSuccessful() || StringUtils.isEmpty(responseEntity.getBody()) || (responseJson = JSONObject.parseObject(responseEntity.getBody())) == null || responseJson.getInteger("errcode") != 0) {
	                List<Employee> employeeDetailByIds = userMapper.getEmployeeDetailByIds(noticedUserIds);
					String userNames = "";
					if (!CollectionUtils.isEmpty(employeeDetailByIds)) {
						userNames = employeeDetailByIds.stream().map(Employee::getUsername).collect(Collectors.joining(","));
					}
	                log.error("给【{}-{}】发送企业微信消息失败，请求参数为:{},返回信息为:{}", noticedUserIds, userNames, JSON.toJSONString(params), JSON.toJSONString(responseEntity));
                }
            } catch (UnsupportedEncodingException e) {
                log.error("发送企业微信消息失败", e);
            }
        });
    }

    private WxMsg buildMsg(NoticeMsgEnum noticeMsgEnum, String processNumber, String formCode, String processName
            , String businessId
            , String taskId
            , String createUserName
            , SyncEhrOrgInfo createUserOrgInfo
            , String corpId
            , String approveDomain
	        , String submitUserId
            , String submitUserName) throws UnsupportedEncodingException {

        WxMsgMarkdown.Markdown markdown = WxMsgMarkdown.Markdown.builder();

        String realApproveUrl = approveUrl.replace("{approveDomain}", approveDomain);
        switch (noticeMsgEnum) {
            case PROCESS_STATE_CHANGE:
	            String flowLink = realApproveUrl +
		            "?formCode=" + formCode +
		            "&processNumber=" + processNumber +
		            "&isOutSideAccess=false&isLowCodeFlow=true";
	            String flowRedirectUrl = wxRedirectUrl.replace("{corpId}", corpId)
		            .replace("{redirectUrl}", encodeURIComponent(flowLink));
	            markdown.add("您的**" + processName + "**申请单发生了新的流转，[可点击查看详情~](" + flowRedirectUrl + ") \n");
                break;
            case PROCESS_APPROVAL:
                String approveLink = realApproveUrl +
                        "?formCode=" + formCode +
                        "&processNumber=" + processNumber +
                        "&taskId=" + taskId +
                        "&isOutSideAccess=false&isLowCodeFlow=true";

                String realRedirectUrl = wxRedirectUrl.replace("{corpId}", corpId)
                        .replace("{redirectUrl}", encodeURIComponent(approveLink));

                markdown.add("**" + createUserName + "**的**" + processName + "**申请待处理，[请前往审批详情页，进行处理~](" + realRedirectUrl + ") \n");
                break;
            case PROCESS_COPY:
                String copyLink = realApproveUrl +
                        "?formCode=" + formCode +
                        "&processNumber=" + processNumber +
                        "&isOutSideAccess=false&isLowCodeFlow=true";

                String realCopyRedirectUrl = wxRedirectUrl.replace("{corpId}", corpId)
                        .replace("{redirectUrl}", encodeURIComponent(copyLink));
                markdown.add("**" + createUserName + "**的**" + processName + "**审批单已抄送给您，[可点击查看详情~](" + realCopyRedirectUrl + ") \n");
                break;
	        case PROCESS_REMINDER:
		        String reminderLink = realApproveUrl +
			        "?formCode=" + formCode +
			        "&processNumber=" + processNumber +
			        "&isOutSideAccess=false&isLowCodeFlow=true";
		        String reminderRedirectUrl = wxRedirectUrl.replace("{corpId}", corpId)
			        .replace("{redirectUrl}", encodeURIComponent(reminderLink));
		        markdown.add("**" + createUserName + "**的**" + processName + "申请催办，请尽快处理~，[可点击查看详情~](" + reminderRedirectUrl + ") \n");
		        break;
            default:
                log.error("未知通知类型");
        }

        if (!noticeMsgEnum.equals(NoticeMsgEnum.PROCESS_STATE_CHANGE)) {
            markdown.add("> 申请单ID：" + businessId + "\n");
            // markdown.add("> 姓名：" + createUserName + "\n");
            // markdown.add("> 部门：" + createUserOrgInfo.getOrgName() + "\n");

	        // 文本控件和成员控件，在创建模板的时候提交人勾选了是，提交表单的时候在bpm_business_process表会有提交人信息。但是文本类型不保存submitUserId，因此submitUserId为空的文本控件不展示提交人所属部门
			if (StringUtils.hasLength(submitUserName)) {
				markdown.add("> 姓名：" + submitUserName + "\n");
			}

	        if (StringUtils.hasLength(submitUserId)) {
		        String orgNameBySubmitterId = ehrSyncService.getOrgNameByUserId(submitUserId);
				if (StringUtils.hasLength(orgNameBySubmitterId))
					markdown.add("> 所属部门：" + orgNameBySubmitterId + "\n");
			}
        }

        WxMsgMarkdown msgMarkdown = new WxMsgMarkdown();
        msgMarkdown.setMarkdown(markdown);
        return msgMarkdown;
    }

    /**
     * 模拟 JavaScript 的 encodeURIComponent 功能
     * @param str 待编码的字符串
     * @return 编码后的字符串
     * @throws UnsupportedEncodingException 若 UTF-8 编码不被支持（理论上不会发生）
     */
    public static String encodeURIComponent(String str) throws UnsupportedEncodingException {
        if (str == null) {
            return "";
        }
        // 使用 UTF-8 编码，然后将空格的 "+" 替换为 "%20"
        return URLEncoder.encode(str, "UTF-8")
                .replace("+", "%20");
    }


    /***
     * 根据userId获取多个企微应用
     * @param businessIds
     * <AUTHOR>
     * @date 2025/2/18 14:05
     * @version 1.0.0
     * @return java.util.List<com.ce.contract.login.model.EhrEmployeeThird>
     **/
    public List<EmployeeInfo> getEmpInfoByBusinessIds(String businessIds) {
        return ehrSyncService.getEmployeeInfoByBusinessIds(businessIds);
    }

    public String getAccessToken(String corpId, String corpsecret) {
        try {
            RequestEntity<Void> build = RequestEntity.get(URI.create(String.format(accessTokenUrl, corpId, corpsecret))).build();
            ResponseEntity<String> exchange = restTemplate.exchange(build, String.class);
            if (!exchange.getStatusCode().is2xxSuccessful()
                    || StringUtils.isEmpty(exchange.getBody())) {
                throw new JiMuBizException(RespCodeEnum.Z0500.getRespCode(), "获取企业微信token失败");
            }
            String body = exchange.getBody();
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.getInteger("errcode") != 0) {
                log.error("获取企业微信token失败:{}, {}", corpId, corpsecret);
                throw new JiMuBizException(RespCodeEnum.Z0500.getRespCode(), "获取企业微信token失败");
            }
            return jsonObject.getString("access_token");
        } catch (Exception e) {
            log.error("获取企业微信token失败", e);
            throw new JiMuBizException(RespCodeEnum.Z0500.getRespCode(), "获取企业微信token失败");
        }
    }

    public WechatConfigProperties.WechatConfig getWechatConfig(EHRSourceEnum EHRSourceEnum) {
        switch (EHRSourceEnum) {
            case JT:
                return wechatConfigProperties.getJituan();
            case CE:
                return wechatConfigProperties.getZhongqi();
            case NEW_NET:
                return wechatConfigProperties.getXinwang();
            case CHEN_XING:
                return wechatConfigProperties.getChenxing();
            case CE_KJ:
                return wechatConfigProperties.getKuajing();
            case XIAO_XIA:
                return wechatConfigProperties.getXiaoxia();
            default:
                log.error("类型非法,找不到对应的企业配置:{}", JSON.toJSONString(EHRSourceEnum));
                throw new JiMuBizException("500", "用户非法,找不到对应的企业配置");
        }
    }

}