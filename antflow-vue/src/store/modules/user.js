import { getToken, removeToken, removeUserInfo, setToken, setUserInfo } from '@/utils/auth'
import { wxLogin, hideLogin } from '@/api/login'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      id: '',
      name: '',
      avatar: '',
      roles: [],
      permissions: []
    }),
    actions: {
      // 登录系统-empId
      empIdLogin(empId, agentId) {
        console.log('empIdLogin-login: ', empId, agentId)
        return new Promise((resolve, reject) => {
          hideLogin({
            empId,
            agentId
          }).then(res => {
            if(res?.data?.token) {
              setToken(res.data.token)
              delete res.data.token
              setUserInfo(res.data)
              resolve()
            } else {
              ElMessage.error(res?.msg || '登录失败，请稍后再试')
              reject(1)
            }
          }).catch(err => {
            console.log('empIdLogin-err: ', err)
            reject(2)
          })
        })
      },
      // 登录系统
      tokenLogin(code, agentId) {
        console.log('tokenLogin-login: ', code, agentId)
        return new Promise((resolve, reject) => {
          wxLogin({
            code,
            agentId
          }).then(res => {
            console.log('tokenLogin-res: ', res)
            if(res?.data?.token) {
              setToken(res.data.token)
              delete res.data.token
              setUserInfo(res.data)
              resolve()
            } else {
              ElMessage.error(res?.msg || '登录失败，请稍后再试')
              reject(1)
            }
          }).catch(err => {
            console.log('tokenLogin-err: ', err)
            reject(2)
          })
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          this.token = ''
          removeToken()
          removeUserInfo()
          resolve()
        })
      }
    }
  })

export default useUserStore
