import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken, getUserInfo } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { isMobile } from '@/utils/constants'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import useUserStore from '@/store/modules/user'
import { wecomCorpListOnline, wecomCorpListTest } from '@/utils/constants'

NProgress.configure({ showSpinner: false });

const whiteList = ['/login', '/empIdLogin'];

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken() && getUserInfo('userId')) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title)
    // 如果已登录但访问的是登录页，则重定向到首页
    if (to.path === '/login') {
      let path = isMobile ? '/wecom/approveList' : '/index'
      next({ path })
      NProgress.done()
      return
    }
    // 如果在白名单中，直接放行
    if (whiteList.indexOf(to.path) !== -1) {
      next()
      return
    }

    // 动态路由处理（核心新增部分）
    const permissionStore = usePermissionStore()
    if (!permissionStore.routesAdded) { // 防止重复添加
      permissionStore.generateRoutes().then(accessRoutes => {
        // 生成可访问的路由表
        accessRoutes.forEach(route => {
          if (!isHttp(route.path)) {
            router.addRoute(route) // 动态添加可访问路由表
          }
        })
        permissionStore.routesAdded = true
        // 重新触发导航以确保新路由生效
        next({ ...to, replace: true })
        NProgress.done()
      }).catch(() => {
        ElMessage.error('菜单加载失败，请刷新页面')
        NProgress.done()
      })
      return
    }
    next()
  } else { // 没有token和用户信息
    // 在白名单中直接放行
    if (whiteList.indexOf(to.path) !== -1) {
      next()
      return
    }
    console.log('没有token和用户信息： ', to)
    let userStore = useUserStore()

    function nextTo() {
      // 其他情况重定向到登录页
      const redirect = to.fullPath;
      if (redirect) {
        next({
          path: '/login',
          query: {
            redirect: encodeURIComponent(redirect)
          }
        })
      } else {
        next('/login')
      }
    }

    const wecomList = window.__ce.ENV === 'prod' ? wecomCorpListOnline : wecomCorpListTest
    let curApp = wecomList.find(item => location.href.includes(item.domainUrl))

    if(to.query && to.query.empId && curApp) {
      Promise.all([
        userStore.empIdLogin(to.query.empId, curApp.agentid),
        permissionStore.generateRoutes(to.query.empId)
      ]).then(() => {
        // 生成可访问的路由表
        accessRoutes.forEach(route => {
          if (!isHttp(route.path)) {
            router.addRoute(route) // 动态添加可访问路由表
          }
        })
        permissionStore.routesAdded = true
        // 重新触发导航以确保新路由生效
        next({ ...to, replace: true })
        NProgress.done()
      }).catch(() => {
        ElMessage.error('菜单加载失败，请刷新页面')
        NProgress.done()
      })
    } else if(to.query && to.query.code && to.query.state === 'approvalProcess' && curApp) {
      userStore.tokenLogin(to.query.code, curApp.agentid).then(res => {
        // 动态路由处理（核心新增部分）
        const permissionStore = usePermissionStore()
        if (!permissionStore.routesAdded) { // 防止重复添加
          permissionStore.generateRoutes().then(accessRoutes => {
            // 生成可访问的路由表
            accessRoutes.forEach(route => {
              if (!isHttp(route.path)) {
                router.addRoute(route) // 动态添加可访问路由表
              }
            })
            permissionStore.routesAdded = true
            // 重新触发导航以确保新路由生效
            next({ ...to, replace: true })
            NProgress.done()
          }).catch(() => {
            ElMessage.error('菜单加载失败，请刷新页面')
            NProgress.done()
          })
          return
        }
        next()
      }).catch(() => {
        nextTo()
      })
    } else {
      nextTo()
    }
    
    NProgress.done()
  }
})

router.afterEach(() => {
  NProgress.done()
})